# GameFlex AWS Backend Startup Script (PowerShell)
# This script starts the AWS backend using LocalStack Pro with Docker-based initialization

param(
    [switch]$Force,
    [switch]$Verbose,
    [switch]$BuildOnly,
    [switch]$SkipBuild
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Load environment variables from .env file
function Load-EnvFile {
    if (Test-Path ".env") {
        Write-Status "Loading environment variables from .env file..."
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^\s*([^#][^=]*)\s*=\s*(.*)\s*$") {
                $name = $matches[1].Trim()
                $value = $matches[2].Trim()
                # Remove quotes if present
                $value = $value -replace '^"(.*)"$', '$1'
                $value = $value -replace "^'(.*)'$", '$1'
                [Environment]::SetEnvironmentVariable($name, $value, "Process")
                if ($Verbose) {
                    Write-Host "  Set $name" -ForegroundColor Gray
                }
            }
        }
    }
    else {
        Write-Warning ".env file not found. Using default values."
    }
}

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[GAMEFLEX] $Message" -ForegroundColor Blue
}

# Check if Docker Desktop is running
function Test-Docker {
    try {
        docker info | Out-Null
        Write-Status "Docker Desktop is running"
        return $true
    }
    catch {
        Write-Error "Docker Desktop is not running. Please start Docker Desktop and try again."
        return $false
    }
}

# Check if required ports are available
function Test-Ports {
    $ports = @(4566, 45660)  # Only LocalStack ports needed
    $portsInUse = @()

    foreach ($port in $ports) {
        $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connection) {
            $portsInUse += $port
            Write-Warning "Port $port is already in use"
        }
    }

    if ($portsInUse.Count -gt 0 -and -not $Force) {
        $response = Read-Host "Do you want to continue anyway? (y/N)"
        if ($response -notmatch "^[Yy]$") {
            Write-Error "Startup cancelled"
            return $false
        }
    }

    Write-Status "Port check completed"
    return $true
}

# Create necessary directories
function New-Directories {
    Write-Status "Creating necessary directories..."
    
    $directories = @(
        "init",
        "lambda-functions",
        "cloudformation", 
        "database\init",
        "logs"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Status "Directories created"
}

# Start Docker services
function Start-Services {
    Write-Header "Starting GameFlex AWS Backend..."

    try {
        # Pull latest images
        Write-Status "Pulling latest Docker images..."
        docker compose pull

        # Start LocalStack service first
        Write-Status "Starting LocalStack service..."
        docker compose up -d localstack

        # Wait for LocalStack to be healthy using Docker health check
        Write-Status "Waiting for LocalStack container to be healthy..."
        $timeout = 180
        $counter = 0

        do {
            $healthStatus = docker inspect gameflex-localstack --format='{{.State.Health.Status}}' 2>$null
            if ($healthStatus -eq "healthy") {
                Write-Status "LocalStack container is healthy"
                break
            }

            Start-Sleep -Seconds 3
            $counter += 3

            if ($counter -ge $timeout) {
                Write-Error "LocalStack failed to become healthy within $timeout seconds"
                Write-Status "Checking LocalStack logs..."
                docker compose logs localstack --tail 20
                throw "LocalStack health check timeout"
            }

            if ($counter % 15 -eq 0) {
                Write-Status "Still waiting for LocalStack... ($counter/$timeout seconds)"
            }
        } while ($true)

        # Additional wait for LocalStack services to be fully ready
        Write-Status "Verifying LocalStack services are ready..."
        $serviceTimeout = 60
        $serviceCounter = 0

        do {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:45660/_localstack/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    $healthData = $response.Content | ConvertFrom-Json
                    $allServicesRunning = $true

                    # Check if core services are running
                    $coreServices = @("dynamodb", "s3", "lambda", "cognito-idp", "iam")
                    foreach ($service in $coreServices) {
                        if ($healthData.services.$service -ne "running") {
                            $allServicesRunning = $false
                            break
                        }
                    }

                    if ($allServicesRunning) {
                        Write-Status "All core LocalStack services are running"
                        break
                    }
                }
            }
            catch {
                # Continue waiting
            }

            Start-Sleep -Seconds 2
            $serviceCounter += 2

            if ($serviceCounter -ge $serviceTimeout) {
                Write-Warning "Some LocalStack services may not be fully ready, but continuing..."
                break
            }
        } while ($true)

        return $true
    }
    catch {
        Write-Host "[ERROR] Failed to start services: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Build Lambda functions using Docker
function Build-LambdaFunctions {
    if ($SkipBuild) {
        Write-Status "Skipping Lambda function build (--SkipBuild specified)"
        return $true
    }

    Write-Status "Building Lambda functions using Docker..."

    try {
        # Ensure LocalStack is still running before building
        $localstackStatus = docker inspect gameflex-localstack --format='{{.State.Status}}' 2>$null
        if ($localstackStatus -ne "running") {
            Write-Error "LocalStack container is not running. Current status: $localstackStatus"
            return $false
        }

        # Run the lambda builder
        Write-Status "Starting Lambda builder container..."
        docker compose --profile build up lambda-builder

        if ($LASTEXITCODE -eq 0) {
            Write-Status "Lambda functions built successfully"

            # Verify packages were created
            if (Test-Path "packages") {
                $packages = Get-ChildItem "packages/*.zip" -ErrorAction SilentlyContinue
                if ($packages.Count -gt 0) {
                    Write-Status "Created $($packages.Count) Lambda packages:"
                    foreach ($package in $packages) {
                        $size = [math]::Round($package.Length / 1MB, 1)
                        Write-Host "  📦 $(Split-Path $package -Leaf) (${size}MB)" -ForegroundColor Cyan
                    }
                }
                else {
                    Write-Warning "No Lambda packages found in packages directory"
                }
            }

            return $true
        }
        else {
            Write-Error "Lambda function build failed"
            Write-Status "Checking build logs..."
            docker compose logs lambda-builder --tail 20
            return $false
        }
    }
    catch {
        Write-Error "Failed to build Lambda functions: $_"
        return $false
    }
}

# Initialize AWS infrastructure using Docker
function Initialize-AwsInfrastructure {
    Write-Status "Initializing AWS infrastructure using Docker..."

    try {
        # Ensure LocalStack is still running and Lambda packages exist
        $localstackStatus = docker inspect gameflex-localstack --format='{{.State.Status}}' 2>$null
        if ($localstackStatus -ne "running") {
            Write-Error "LocalStack container is not running. Current status: $localstackStatus"
            return $false
        }

        # Check if Lambda packages exist
        if (!(Test-Path "packages") -or (Get-ChildItem "packages/*.zip" -ErrorAction SilentlyContinue).Count -eq 0) {
            Write-Error "No Lambda packages found. Please build Lambda functions first."
            return $false
        }

        # Run the AWS initializer
        Write-Status "Starting AWS infrastructure initialization..."
        docker compose --profile init up aws-initializer

        if ($LASTEXITCODE -eq 0) {
            Write-Status "AWS infrastructure initialized successfully"
            return $true
        }
        else {
            Write-Error "AWS infrastructure initialization failed"
            Write-Status "Checking initialization logs..."
            docker compose logs aws-initializer --tail 20
            return $false
        }
    }
    catch {
        Write-Error "Failed to initialize AWS infrastructure: $_"
        return $false
    }
}

# Legacy function - now handled by Docker containers
function Initialize-AwsServices {
    Write-Status "AWS services initialization is now handled by Docker containers"
    Write-Status "Use the new Docker-based approach instead"
}

# Handle Force mode - restart LocalStack completely
function Reset-LocalStack {
    if ($Force) {
        Write-Status "Force mode enabled - restarting LocalStack completely..."
        try {
            docker compose down
            Start-Sleep -Seconds 5

            # Clean up any orphaned containers
            docker system prune -f

            Write-Status "LocalStack reset completed"
            return $true
        }
        catch {
            Write-Error "Failed to reset LocalStack: $_"
            return $false
        }
    }
    return $true
}

# Legacy cleanup code removed - now handled by Docker containers





# Legacy function - now handled by Docker containers
function Deploy-Infrastructure {
    Write-Status "Infrastructure deployment is now handled by Docker containers"
    Write-Status "Use the new Docker-based approach instead"
    return $true
}

# Display service information
function Show-ServiceInfo {
    Write-Header "GameFlex AWS Backend (Docker-based) is now running!"
    Write-Host ""

    # Get API Gateway URL from CloudFormation stack outputs
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    $env:AWS_DEFAULT_REGION = "us-east-1"
    $ENDPOINT_URL = "http://localhost:45660"

    try {
        $apiUrl = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].Outputs[?OutputKey=='ApiGatewayUrl'].OutputValue" --output text 2>$null
        if ($apiUrl) {
            Write-Host "  API Gateway URL: $apiUrl" -ForegroundColor Cyan
        }
    }
    catch {
        # Continue if we can't get the API URL
    }

    Write-Status "Service URLs:"
    Write-Host "  LocalStack Health: http://localhost:45660/_localstack/health" -ForegroundColor Cyan
    Write-Host "  LocalStack Pro Dashboard: http://localhost:4566/_localstack/health" -ForegroundColor Cyan
    Write-Host "  DynamoDB (via LocalStack): http://localhost:4566" -ForegroundColor Cyan
    Write-Host "  S3 Console: http://localhost:45660/_aws/s3" -ForegroundColor Cyan
    Write-Host "  Cognito Console: http://localhost:45660/_aws/cognito" -ForegroundColor Cyan
    Write-Host ""

    Write-Status "Development Credentials:"
    Write-Host "  Developer: <EMAIL> / DevPassword123!" -ForegroundColor Cyan
    Write-Host "  Admin: <EMAIL> / AdminPassword123!" -ForegroundColor Cyan
    Write-Host ""

    Write-Status "Useful Commands:"
    Write-Host "  Check status: docker compose ps" -ForegroundColor Cyan
    Write-Host "  View logs: docker compose logs -f localstack" -ForegroundColor Cyan
    Write-Host "  Build only: .\start.ps1 -BuildOnly" -ForegroundColor Cyan
    Write-Host "  Skip build: .\start.ps1 -SkipBuild" -ForegroundColor Cyan
    Write-Host "  Stop services: .\stop.ps1" -ForegroundColor Cyan
    Write-Host "  Restart: .\stop.ps1; .\start.ps1" -ForegroundColor Cyan
    Write-Host ""

    Write-Status "Docker Services:"
    docker compose ps
    Write-Host ""

    Write-Warning "This is a development environment. Do not use in production!"
}

# Main execution
function Main {
    Write-Header "GameFlex AWS Backend Startup (Docker-based)"
    Write-Host ""

    # Load environment variables from .env file
    Load-EnvFile

    if (-not (Test-Docker)) {
        exit 1
    }

    if (-not (Test-Ports)) {
        exit 1
    }

    New-Directories

    # Handle Force mode
    if (-not (Reset-LocalStack)) {
        exit 1
    }

    # Start LocalStack service and wait for it to be ready
    Write-Status "Step 1: Starting LocalStack service..."
    if (-not (Start-Services)) {
        Write-Error "Failed to start LocalStack service"
        exit 1
    }

    # If BuildOnly is specified, just build and exit
    if ($BuildOnly) {
        Write-Status "Build-only mode specified"
        Write-Status "Step 2: Building Lambda functions..."
        if (-not (Build-LambdaFunctions)) {
            Write-Error "Lambda function build failed"
            exit 1
        }
        Write-Status "Build completed successfully!"
        return
    }

    # Build Lambda functions
    Write-Status "Step 2: Building Lambda functions..."
    if (-not (Build-LambdaFunctions)) {
        Write-Error "Lambda function build failed."
        Write-Status "You can check the build logs with:"
        Write-Host "  docker compose logs lambda-builder" -ForegroundColor Cyan
        exit 1
    }

    # Initialize AWS infrastructure
    Write-Status "Step 3: Initializing AWS infrastructure..."
    if (-not (Initialize-AwsInfrastructure)) {
        Write-Error "AWS infrastructure initialization failed."
        Write-Status "You can check the logs with:"
        Write-Host "  docker compose logs aws-initializer" -ForegroundColor Cyan
        Write-Status "Or try running with -Force to restart LocalStack completely:"
        Write-Host "  .\stop.ps1; .\start.ps1 -Force" -ForegroundColor Cyan
        exit 1
    }

    Show-ServiceInfo

    Write-Status "Startup completed successfully!"
}

# Run main function
try {
    Main
}
catch {
    Write-Host "[ERROR] Startup failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
