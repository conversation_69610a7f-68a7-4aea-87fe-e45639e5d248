#!/bin/bash

# GameFlex Lambda Function Builder Script
# This script builds all TypeScript Lambda functions and creates deployment packages

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[LAMBDA-BUILDER]${NC} $1"
}

# Function to build a single Lambda function
build_lambda_function() {
    local function_name=$1
    local function_dir=$2

    log_header "Building Lambda function: $function_name"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    # Check if function directory exists
    if [ ! -d "/workspace/lambda-functions/$function_dir" ]; then
        log_warn "Lambda function directory not found: /workspace/lambda-functions/$function_dir"
        return 0
    fi

    cd "/workspace/lambda-functions/$function_dir"

    # Clean previous builds
    log_info "🧹 Cleaning previous builds for $function_name..."
    rm -rf dist node_modules package-lock.json *.zip

    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        log_error "❌ package.json not found for $function_name"
        return 1
    fi

    # Show package.json info
    log_info "📋 Package info for $function_name:"
    if command -v jq >/dev/null 2>&1; then
        echo "   Name: $(jq -r '.name // "unknown"' package.json)"
        echo "   Version: $(jq -r '.version // "unknown"' package.json)"
    else
        echo "   $(head -5 package.json | grep -E '"name"|"version"')"
    fi

    # Install all dependencies (including devDependencies for building)
    log_info "📦 Installing all dependencies for $function_name..."
    echo "   This may take a moment..."

    # First try npm ci for faster, reliable installs
    if [ -f "package-lock.json" ]; then
        log_info "   Using npm ci (faster, uses package-lock.json)..."
        npm ci --include=dev 2>&1 | while IFS= read -r line; do
            echo "   📦 $line"
        done
        npm_exit_code=${PIPESTATUS[0]}
    else
        log_info "   Using npm install (no package-lock.json found)..."
        npm install --include=dev 2>&1 | while IFS= read -r line; do
            echo "   📦 $line"
        done
        npm_exit_code=${PIPESTATUS[0]}
    fi

    if [ $npm_exit_code -ne 0 ]; then
        log_error "❌ Failed to install dependencies for $function_name"
        return 1
    fi

    # Ensure required TypeScript dependencies are installed
    log_info "🔧 Ensuring TypeScript dependencies are available..."

    # Check for required packages and install if missing
    local required_packages=("typescript" "@types/node" "@types/aws-lambda")
    local missing_packages=()

    for package in "${required_packages[@]}"; do
        # Check if package actually exists in node_modules (more reliable than package.json check)
        if [ ! -d "node_modules/$package" ]; then
            missing_packages+=("$package")
        fi
    done

    if [ ${#missing_packages[@]} -gt 0 ]; then
        log_warn "⚠️  Missing required packages: ${missing_packages[*]}"
        log_info "📦 Installing missing TypeScript dependencies..."

        # Install each package individually to ensure they're added
        for package in "${missing_packages[@]}"; do
            echo "   📦 Installing $package..."
            npm install --save-dev "$package" --force
            if [ $? -ne 0 ]; then
                log_error "❌ Failed to install $package"
                return 1
            fi
        done

        log_info "✅ Missing TypeScript dependencies installed successfully"
    else
        log_info "✅ All required TypeScript dependencies are available"
    fi
    
    # Build TypeScript
    log_info "🔨 Building TypeScript for $function_name..."

    # Show source files being compiled
    if [ -d "src" ]; then
        local ts_files=$(find src -name "*.ts" | wc -l)
        log_info "   Found $ts_files TypeScript files to compile"
    fi

    local build_success=false

    if [ -f "tsconfig.json" ]; then
        log_info "   Using tsconfig.json configuration"
        echo "   🔨 Compiling TypeScript..."
        npx tsc 2>&1 | while IFS= read -r line; do
            echo "   🔨 $line"
        done
        if [ ${PIPESTATUS[0]} -eq 0 ]; then
            build_success=true
        fi
    elif npm run build >/dev/null 2>&1; then
        log_info "   Using npm run build script"
        echo "   🔨 Running build script..."
        npm run build 2>&1 | while IFS= read -r line; do
            echo "   🔨 $line"
        done
        if [ ${PIPESTATUS[0]} -eq 0 ]; then
            build_success=true
        fi
    else
        log_warn "   No tsconfig.json or build script found, using default TypeScript compilation"
        echo "   🔨 Compiling with default settings..."

        # Create src directory if it doesn't exist and there are .ts files in root
        if [ ! -d "src" ] && ls *.ts >/dev/null 2>&1; then
            log_info "   Moving .ts files to src/ directory"
            mkdir -p src
            mv *.ts src/
        fi

        if [ -d "src" ]; then
            npx tsc --target es2020 --module commonjs --outDir dist --rootDir src --strict --esModuleInterop --skipLibCheck src/*.ts 2>&1 | while IFS= read -r line; do
                echo "   🔨 $line"
            done
            if [ ${PIPESTATUS[0]} -eq 0 ]; then
                build_success=true
            fi
        else
            log_error "   No TypeScript source files found"
            return 1
        fi
    fi

    if [ "$build_success" != "true" ]; then
        log_error "❌ TypeScript build failed for $function_name"
        return 1
    fi
    
    # Verify dist directory was created
    if [ ! -d "dist" ]; then
        log_error "❌ Build output directory 'dist' not found for $function_name"
        return 1
    fi

    # Show what was built
    local js_files=$(find dist -name "*.js" | wc -l)
    log_info "✅ TypeScript compilation successful! Generated $js_files JavaScript files"

    # Create deployment package with proper structure
    log_info "📦 Creating deployment package for $function_name..."

    # Create temporary directory for packaging
    local temp_dir="/tmp/${function_name}-package"
    rm -rf "$temp_dir"
    mkdir -p "$temp_dir"

    # Copy built JavaScript files
    log_info "   📁 Copying compiled JavaScript files..."
    cp -r dist/* "$temp_dir/"

    # Copy package.json for production dependency installation
    log_info "   📋 Copying package configuration..."
    cp package.json "$temp_dir/"
    [ -f "package-lock.json" ] && cp package-lock.json "$temp_dir/"

    # Install production dependencies in temp directory
    cd "$temp_dir"
    log_info "   📦 Installing production dependencies for deployment..."
    echo "   This may take a moment..."

    if [ -f "package-lock.json" ]; then
        npm ci --production 2>&1 | while IFS= read -r line; do
            echo "   📦 $line"
        done
        npm_prod_exit=${PIPESTATUS[0]}
    else
        npm install --production 2>&1 | while IFS= read -r line; do
            echo "   📦 $line"
        done
        npm_prod_exit=${PIPESTATUS[0]}
    fi

    if [ $npm_prod_exit -ne 0 ]; then
        log_error "❌ Failed to install production dependencies for $function_name"
        return 1
    fi

    # Clean up unnecessary files for deployment
    log_info "   🧹 Cleaning up unnecessary files for deployment..."
    rm -f package.json package-lock.json
    find . -name "*.map" -delete
    find . -name "*.d.ts" -delete
    find . -name "*.ts" -delete
    find . -name "tsconfig.json" -delete
    find . -name "README.md" -delete
    find . -name ".gitignore" -delete

    # Show package contents summary
    local total_files=$(find . -type f | wc -l)
    local js_files_final=$(find . -name "*.js" | wc -l)
    log_info "   📊 Package contains $total_files files ($js_files_final JavaScript files)"

    # Create zip package
    log_info "   🗜️  Creating ZIP package..."
    zip -r "/workspace/packages/${function_name}.zip" . -q

    # Clean up temporary directory
    rm -rf "$temp_dir"

    # Verify package was created and show size
    if [ -f "/workspace/packages/${function_name}.zip" ]; then
        local package_size=$(du -h "/workspace/packages/${function_name}.zip" | cut -f1)
        log_info "✅ Successfully built Lambda function: $function_name (${package_size})"

        # Show package contents for verification
        log_info "   📋 Package contents:"
        unzip -l "/workspace/packages/${function_name}.zip" | head -10 | tail -n +4 | while IFS= read -r line; do
            echo "      $line"
        done
        local total_entries=$(unzip -l "/workspace/packages/${function_name}.zip" | grep -c "^[[:space:]]*[0-9]")
        if [ $total_entries -gt 6 ]; then
            echo "      ... and $((total_entries - 6)) more files"
        fi
    else
        log_error "❌ Failed to create package for $function_name"
        return 1
    fi

    cd /workspace
    echo ""
}

# Main execution
log_header "🚀 Starting GameFlex Lambda Function Build Process"
echo "════════════════════════════════════════════════════════════════════════════════"

# Create packages directory if it doesn't exist
mkdir -p /workspace/packages

# Clean previous packages
log_info "🧹 Cleaning previous packages..."
if [ -n "$(ls -A /workspace/packages/ 2>/dev/null)" ]; then
    rm -f /workspace/packages/*.zip
    log_info "   Removed existing packages"
else
    log_info "   No previous packages to clean"
fi

# Show environment info
log_info "🔧 Build environment:"
echo "   Node.js: $(node --version)"
echo "   NPM: $(npm --version)"
echo "   TypeScript: $(npx tsc --version)"
echo ""

# Build all Lambda functions
log_header "📦 Building Lambda Functions"
echo "════════════════════════════════════════════════════════════════════════════════"

# Track build results
declare -a successful_builds=()
declare -a failed_builds=()
declare -a build_times=()

# Function to time builds
time_build() {
    local start_time=$(date +%s)
    local func_name=$1
    local func_dir=$2

    if build_lambda_function "$func_name" "$func_dir"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        successful_builds+=("$func_dir")
        build_times+=("$func_dir:${duration}s")
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        failed_builds+=("$func_dir")
        build_times+=("$func_dir:${duration}s (FAILED)")
        return 1
    fi
}

# Build auth function
log_header "🔐 Building Authentication Function"
time_build "gameflex-auth-development" "auth"

# Build posts function
log_header "📝 Building Posts Function"
time_build "gameflex-posts-development" "posts"

# Build users function
log_header "👥 Building Users Function"
time_build "gameflex-users-development" "users"

# Build media function
log_header "🎬 Building Media Function"
time_build "gameflex-media-development" "media"

# Report results
echo ""
echo "════════════════════════════════════════════════════════════════════════════════"
log_header "📊 Build Summary Report"
echo "════════════════════════════════════════════════════════════════════════════════"

log_info "✅ Successful builds: ${#successful_builds[@]}"
for func in "${successful_builds[@]}"; do
    log_info "  ✓ $func"
done

if [ ${#failed_builds[@]} -gt 0 ]; then
    log_error "❌ Failed builds: ${#failed_builds[@]}"
    for func in "${failed_builds[@]}"; do
        log_error "  ✗ $func"
    done
fi

# Show build times
log_info "⏱️  Build times:"
for time_info in "${build_times[@]}"; do
    echo "  📊 $time_info"
done

# Show final package information
if [ ${#successful_builds[@]} -gt 0 ]; then
    log_info "📦 Generated packages:"
    ls -la /workspace/packages/ | grep "\.zip$" | while IFS= read -r line; do
        echo "  📦 $line"
    done

    # Calculate total package size
    total_size=$(du -sh /workspace/packages/ 2>/dev/null | cut -f1)
    log_info "💾 Total package size: $total_size"
fi

if [ ${#failed_builds[@]} -gt 0 ]; then
    echo ""
    log_error "❌ Build process completed with errors!"
    log_error "Please check the error messages above and fix the issues."
    exit 1
fi

echo ""
log_header "🎉 All Lambda functions built successfully!"
log_info "🚀 Deployment packages are ready in /workspace/packages/"
echo "════════════════════════════════════════════════════════════════════════════════"
