{"name": "gameflex-auth-lambda", "version": "1.0.0", "description": "GameFlex Authentication Lambda Functions", "main": "dist/handler.js", "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "package": "npm run build && npm prune --production"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.450.0", "@aws-sdk/client-dynamodb": "^3.450.0", "@aws-sdk/lib-dynamodb": "^3.450.0", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/aws-lambda": "^8.10.150", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.19.7", "@types/uuid": "^9.0.7", "rimraf": "^5.0.5", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}