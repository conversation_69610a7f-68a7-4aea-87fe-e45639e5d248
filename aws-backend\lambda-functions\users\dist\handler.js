"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_cognito_identity_provider_1 = require("@aws-sdk/client-cognito-identity-provider");
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const cognitoClient = new client_cognito_identity_provider_1.CognitoIdentityProviderClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});
const dynamoClient = new client_dynamodb_1.DynamoDBClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});
const docClient = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamoClient);
const USERS_TABLE = 'Users';
const USER_PROFILES_TABLE = 'UserProfiles';
const POSTS_TABLE = 'Posts';
const LIKES_TABLE = 'Likes';
const FOLLOWS_TABLE = 'Follows';
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Credentials': 'true'
};
function createCorsResponse(statusCode, body) {
    return {
        statusCode,
        headers: corsHeaders,
        body: JSON.stringify(body)
    };
}
function extractUserIdFromToken(authHeader) {
    try {
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return null;
        }
        const token = authHeader.substring(7);
        const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        return payload.sub || payload['cognito:username'] || null;
    }
    catch (error) {
        console.error('Error extracting user ID from token:', error);
        return null;
    }
}
async function getUserByCognitoId(cognitoUserId) {
    try {
        const result = await docClient.send(new lib_dynamodb_1.ScanCommand({
            TableName: USERS_TABLE,
            FilterExpression: 'cognito_user_id = :cognitoUserId',
            ExpressionAttributeValues: {
                ':cognitoUserId': cognitoUserId
            }
        }));
        return result.Items && result.Items.length > 0 ? result.Items[0] : null;
    }
    catch (error) {
        console.error('Error getting user by Cognito ID:', error);
        return null;
    }
}
async function getUserById(userId) {
    try {
        const result = await docClient.send(new lib_dynamodb_1.GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        }));
        return result.Item || null;
    }
    catch (error) {
        console.error('Error getting user by ID:', error);
        return null;
    }
}
async function getUserProfile(userId) {
    try {
        const result = await docClient.send(new lib_dynamodb_1.GetCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: userId }
        }));
        return result.Item || null;
    }
    catch (error) {
        console.error('Error getting user profile:', error);
        return null;
    }
}
async function getUserStats(userId) {
    try {
        const postsResult = await docClient.send(new lib_dynamodb_1.ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: 'user_id = :userId AND is_active = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            },
            Select: 'COUNT'
        }));
        const likesResult = await docClient.send(new lib_dynamodb_1.ScanCommand({
            TableName: LIKES_TABLE,
            FilterExpression: 'user_id = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        }));
        const followersResult = await docClient.send(new lib_dynamodb_1.ScanCommand({
            TableName: FOLLOWS_TABLE,
            FilterExpression: 'following_id = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        }));
        const followingResult = await docClient.send(new lib_dynamodb_1.ScanCommand({
            TableName: FOLLOWS_TABLE,
            FilterExpression: 'follower_id = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        }));
        return {
            posts: postsResult.Count || 0,
            likes: likesResult.Count || 0,
            followers: followersResult.Count || 0,
            following: followingResult.Count || 0
        };
    }
    catch (error) {
        console.error('Error getting user stats:', error);
        return { posts: 0, likes: 0, followers: 0, following: 0 };
    }
}
async function getUserPosts(userId, limit = 20, offset = 0) {
    try {
        const result = await docClient.send(new lib_dynamodb_1.ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: 'user_id = :userId AND is_active = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            }
        }));
        const posts = (result.Items || [])
            .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
            .slice(offset, offset + limit);
        return posts;
    }
    catch (error) {
        console.error('Error getting user posts:', error);
        return [];
    }
}
const handler = async (event, context) => {
    console.log('Users Lambda - Event:', JSON.stringify(event, null, 2));
    if (event.httpMethod === 'OPTIONS') {
        return createCorsResponse(200, { message: 'CORS preflight successful' });
    }
    const path = event.path;
    const method = event.httpMethod;
    const authHeader = event.headers.Authorization || event.headers.authorization;
    try {
        let currentUserId = null;
        if (authHeader) {
            currentUserId = extractUserIdFromToken(authHeader);
        }
        if (method === 'GET' && path.match(/^\/users\/profile\/[^\/]+$/)) {
            const userId = path.split('/').pop();
            const user = await getUserById(userId);
            if (!user) {
                return createCorsResponse(404, { error: 'User not found' });
            }
            const profile = await getUserProfile(userId);
            const stats = await getUserStats(userId);
            return createCorsResponse(200, {
                user,
                profile,
                stats
            });
        }
        if (method === 'GET' && path.match(/^\/users\/posts\/[^\/]+$/)) {
            const userId = path.split('/').pop();
            const limit = parseInt(event.queryStringParameters?.limit || '20');
            const offset = parseInt(event.queryStringParameters?.offset || '0');
            const posts = await getUserPosts(userId, limit, offset);
            return createCorsResponse(200, { posts });
        }
        if (method === 'PUT' && path === '/users/profile') {
            if (!currentUserId) {
                return createCorsResponse(401, { error: 'Authentication required' });
            }
            const user = await getUserByCognitoId(currentUserId);
            if (!user) {
                return createCorsResponse(404, { error: 'User not found' });
            }
            const body = JSON.parse(event.body || '{}');
            const now = new Date().toISOString();
            const userUpdates = { updated_at: now };
            if (body.display_name !== undefined)
                userUpdates.display_name = body.display_name;
            if (body.username !== undefined)
                userUpdates.username = body.username;
            if (body.bio !== undefined)
                userUpdates.bio = body.bio;
            if (body.avatar_url !== undefined)
                userUpdates.avatar_url = body.avatar_url;
            if (Object.keys(userUpdates).length > 1) {
                await docClient.send(new lib_dynamodb_1.UpdateCommand({
                    TableName: USERS_TABLE,
                    Key: { id: user.id },
                    UpdateExpression: `SET ${Object.keys(userUpdates).map(key => `#${key} = :${key}`).join(', ')}`,
                    ExpressionAttributeNames: Object.keys(userUpdates).reduce((acc, key) => {
                        acc[`#${key}`] = key;
                        return acc;
                    }, {}),
                    ExpressionAttributeValues: Object.keys(userUpdates).reduce((acc, key) => {
                        acc[`:${key}`] = userUpdates[key];
                        return acc;
                    }, {})
                }));
            }
            const profileUpdates = { updated_at: now };
            if (body.first_name !== undefined)
                profileUpdates.first_name = body.first_name;
            if (body.last_name !== undefined)
                profileUpdates.last_name = body.last_name;
            if (body.country !== undefined)
                profileUpdates.country = body.country;
            if (body.timezone !== undefined)
                profileUpdates.timezone = body.timezone;
            if (body.language !== undefined)
                profileUpdates.language = body.language;
            if (Object.keys(profileUpdates).length > 1) {
                const existingProfile = await getUserProfile(user.id);
                if (existingProfile) {
                    await docClient.send(new lib_dynamodb_1.UpdateCommand({
                        TableName: USER_PROFILES_TABLE,
                        Key: { user_id: user.id },
                        UpdateExpression: `SET ${Object.keys(profileUpdates).map(key => `#${key} = :${key}`).join(', ')}`,
                        ExpressionAttributeNames: Object.keys(profileUpdates).reduce((acc, key) => {
                            acc[`#${key}`] = key;
                            return acc;
                        }, {}),
                        ExpressionAttributeValues: Object.keys(profileUpdates).reduce((acc, key) => {
                            acc[`:${key}`] = profileUpdates[key];
                            return acc;
                        }, {})
                    }));
                }
                else {
                    await docClient.send(new lib_dynamodb_1.PutCommand({
                        TableName: USER_PROFILES_TABLE,
                        Item: {
                            user_id: user.id,
                            created_at: now,
                            ...profileUpdates
                        }
                    }));
                }
            }
            return createCorsResponse(200, { message: 'Profile updated successfully' });
        }
        return createCorsResponse(404, { error: 'Endpoint not found' });
    }
    catch (error) {
        console.error('Users Lambda error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
exports.handler = handler;
