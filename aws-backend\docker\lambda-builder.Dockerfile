# Lambda Builder Docker Image
# This image contains all tools needed to build TypeScript Lambda functions
FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache \
    zip \
    unzip \
    curl \
    bash \
    python3 \
    py3-pip \
    aws-cli \
    git \
    make \
    g++ \
    jq

# Install global npm packages for TypeScript compilation
RUN npm install -g typescript @types/node ts-node

# Create working directory
WORKDIR /workspace

# Create directories for scripts and packages
RUN mkdir -p /usr/local/bin /workspace/packages

# Set proper permissions
RUN chmod 755 /usr/local/bin

# Set default command (will be overridden by docker-compose)
CMD ["bash", "/usr/local/bin/build-all-lambdas.sh"]
