"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_cognito_identity_provider_1 = require("@aws-sdk/client-cognito-identity-provider");
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const uuid_1 = require("uuid");
const cognitoClient = new client_cognito_identity_provider_1.CognitoIdentityProviderClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});
const dynamoClient = new client_dynamodb_1.DynamoDBClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});
const docClient = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamoClient);
const MEDIA_BUCKET = process.env.S3_BUCKET_MEDIA || 'gameflex-media-development';
const USERS_TABLE = 'Users';
const POSTS_TABLE = 'Posts';
const MEDIA_TABLE = 'Media';
const CHANNEL_MEMBERS_TABLE = 'ChannelMembers';
function createCorsResponse(statusCode, body) {
    return {
        statusCode,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Amz-Date, X-Api-Key, X-Amz-Security-Token'
        },
        body: JSON.stringify(body)
    };
}
async function getUserFromToken(accessToken) {
    try {
        const getUserCommand = new client_cognito_identity_provider_1.GetUserCommand({
            AccessToken: accessToken
        });
        const response = await cognitoClient.send(getUserCommand);
        const cognitoUserId = response.Username;
        const result = await docClient.send(new lib_dynamodb_1.QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'cognito_user_id-index',
            KeyConditionExpression: 'cognito_user_id = :cognitoUserId',
            FilterExpression: 'is_active = :isActive',
            ExpressionAttributeValues: {
                ':cognitoUserId': cognitoUserId,
                ':isActive': true
            }
        }));
        return result.Items && result.Items.length > 0 ? result.Items[0] : null;
    }
    catch (error) {
        console.error('Failed to get user from token:', error);
        return null;
    }
}
async function getPostsHandler(event) {
    try {
        const queryParams = event.queryStringParameters || {};
        const limit = Math.min(parseInt(queryParams.limit || '20'), 100);
        const offset = parseInt(queryParams.offset || '0');
        const channelId = queryParams.channel_id;
        const authorId = queryParams.author_id;
        const whereConditions = ["p.visibility = 'public'"];
        const params = [];
        let paramIndex = 1;
        if (channelId) {
            whereConditions.push(`p.channel_id = $${paramIndex++}`);
            params.push(channelId);
        }
        if (authorId) {
            whereConditions.push(`p.author_id = $${paramIndex++}`);
            params.push(authorId);
        }
        const whereClause = whereConditions.join(' AND ');
        return createCorsResponse(200, {
            posts: [],
            total: 0,
            limit,
            offset
        });
    }
    catch (error) {
        console.error('Get posts handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
async function createPostHandler(event) {
    try {
        const authHeader = event.headers.Authorization || event.headers.authorization || '';
        if (!authHeader.startsWith('Bearer ')) {
            return createCorsResponse(401, {
                error: 'Authorization header required'
            });
        }
        const accessToken = authHeader.substring(7);
        const user = await getUserFromToken(accessToken);
        if (!user) {
            return createCorsResponse(401, {
                error: 'Invalid or expired token'
            });
        }
        console.log('Raw event body:', event.body);
        let body;
        try {
            let bodyStr = event.body || '{}';
            if (typeof bodyStr === 'string' && bodyStr.startsWith('"') && bodyStr.endsWith('"')) {
                bodyStr = JSON.parse(bodyStr);
            }
            body = JSON.parse(bodyStr);
        }
        catch (parseError) {
            console.error('JSON parse error:', parseError);
            console.error('Body content:', event.body);
            return createCorsResponse(400, {
                error: 'Invalid JSON in request body'
            });
        }
        const content = body.content?.trim();
        const channelId = body.channel_id;
        const mediaId = body.media_id;
        if (!content) {
            return createCorsResponse(400, {
                error: 'Content is required'
            });
        }
        if (channelId) {
            const result = await docClient.send(new lib_dynamodb_1.GetCommand({
                TableName: CHANNEL_MEMBERS_TABLE,
                Key: {
                    channel_id: channelId,
                    user_id: user.id
                }
            }));
            if (!result.Item) {
                return createCorsResponse(403, {
                    error: 'You are not a member of this channel'
                });
            }
        }
        const postId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const post = {
            id: postId,
            content: content,
            media_id: mediaId,
            author_id: user.id,
            channel_id: channelId,
            like_count: 0,
            comment_count: 0,
            view_count: 0,
            created_at: now,
            updated_at: now
        };
        await docClient.send(new lib_dynamodb_1.PutCommand({
            TableName: POSTS_TABLE,
            Item: post
        }));
        return createCorsResponse(201, {
            message: 'Post created successfully',
            post: {
                id: post.id,
                content: post.content,
                like_count: post.like_count,
                comment_count: post.comment_count,
                view_count: post.view_count,
                created_at: post.created_at,
                updated_at: post.updated_at,
                author: {
                    id: user.id,
                    username: user.username,
                    display_name: user.display_name,
                    avatar_url: user.avatar_url
                }
            }
        });
    }
    catch (error) {
        console.error('Create post handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
async function getPostHandler(event) {
    try {
        const postId = event.pathParameters?.id;
        if (!postId) {
            return createCorsResponse(400, {
                error: 'Post ID is required'
            });
        }
        const result = await docClient.send(new lib_dynamodb_1.GetCommand({
            TableName: POSTS_TABLE,
            Key: { id: postId }
        }));
        if (!result.Item) {
            return createCorsResponse(404, {
                error: 'Post not found'
            });
        }
        const post = result.Item;
        return createCorsResponse(200, {
            post: {
                id: post.id,
                content: post.content,
                like_count: post.like_count || 0,
                comment_count: post.comment_count || 0,
                view_count: (post.view_count || 0) + 1,
                created_at: post.created_at,
                updated_at: post.updated_at,
                author_id: post.author_id,
                channel_id: post.channel_id,
                media_id: post.media_id
            }
        });
    }
    catch (error) {
        console.error('Get post handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
const handler = async (event, context) => {
    if (event.httpMethod === 'OPTIONS') {
        return createCorsResponse(200, {});
    }
    const path = event.path;
    const method = event.httpMethod;
    try {
        if (path === '/posts' && method === 'GET') {
            return await getPostsHandler(event);
        }
        else if (path === '/posts' && method === 'POST') {
            return await createPostHandler(event);
        }
        else if (path.startsWith('/posts/') && method === 'GET') {
            return await getPostHandler(event);
        }
        else {
            return createCorsResponse(404, {
                error: 'Endpoint not found'
            });
        }
    }
    catch (error) {
        console.error('Lambda handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
};
exports.handler = handler;
